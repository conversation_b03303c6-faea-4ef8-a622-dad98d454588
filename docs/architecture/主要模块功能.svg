<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1082.21875 605.78125" width="2164.4375" height="1211.5625"><!-- svg-source:excalidraw --><metadata></metadata><defs><symbol id="image-NqfPPR9vs4trTxAleH_Sr"><image href="data:image/svg+xml;base64,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" preserveAspectRatio="none" width="100%" height="100%"></image></symbol><style class="style-fonts">
      </style></defs><rect x="0" y="0" width="1082.21875" height="605.78125" fill="#ffffff"></rect><g transform="translate(10 10) rotate(0 531.109375 292.890625)"><use href="#image-NqfPPR9vs4trTxAleH_Sr" width="1062" height="586" opacity="1"></use></g></svg>