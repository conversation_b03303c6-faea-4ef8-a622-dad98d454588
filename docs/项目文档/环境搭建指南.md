# 开发环境搭建详细指南

## 🎯 环境搭建目标

搭建一个完整、稳定的Java企业级开发环境，支持Spring Boot项目的开发、调试、测试和部署。

## 💻 系统要求

### 硬件要求
- **CPU**: Intel i5 或 AMD Ryzen 5 以上
- **内存**: 8GB RAM 最低，16GB 推荐
- **硬盘**: 至少50GB可用空间（SSD推荐）
- **网络**: 稳定的互联网连接

### 操作系统支持
- **Windows**: Windows 10/11 (64位)
- **macOS**: macOS 10.14+ 
- **Linux**: Ubuntu 18.04+, CentOS 7+

## ☕ Java开发环境

### 1. JDK安装

#### Windows系统
```powershell
# 方法1：官网下载安装
# 1. 访问 https://www.oracle.com/java/technologies/downloads/
# 2. 下载 JDK 17 Windows x64 Installer
# 3. 运行安装程序，按默认设置安装

# 方法2：使用包管理器（推荐）
# 安装 Chocolatey
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装 JDK
choco install openjdk17
```

#### macOS系统
```bash
# 方法1：使用 Homebrew（推荐）
# 安装 Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装 JDK
brew install openjdk@17

# 创建符号链接
sudo ln -sfn /opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-17.jdk

# 方法2：官网下载
# 访问 https://www.oracle.com/java/technologies/downloads/
# 下载 JDK 17 macOS Installer
```

#### Linux系统（Ubuntu）
```bash
# 更新包列表
sudo apt update

# 安装 OpenJDK 17
sudo apt install openjdk-17-jdk

# 或者安装 Oracle JDK
# 添加 Oracle JDK PPA
sudo add-apt-repository ppa:linuxuprising/java
sudo apt update
sudo apt install oracle-java17-installer
```

### 2. 环境变量配置

#### Windows系统
```powershell
# 设置 JAVA_HOME
setx JAVA_HOME "C:\Program Files\Java\jdk-17"

# 添加到 PATH
setx PATH "%PATH%;%JAVA_HOME%\bin"

# 验证安装
java -version
javac -version
```

#### macOS/Linux系统
```bash
# 编辑 ~/.bashrc 或 ~/.zshrc
nano ~/.bashrc

# 添加以下内容
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64  # Linux
# export JAVA_HOME=/opt/homebrew/opt/openjdk@17        # macOS

export PATH=$JAVA_HOME/bin:$PATH

# 重新加载配置
source ~/.bashrc

# 验证安装
java -version
javac -version
```

## 🛠️ 开发工具安装

### 1. IntelliJ IDEA

#### 下载安装
```bash
# 访问官网下载
# https://www.jetbrains.com/idea/download/

# Windows: 下载 .exe 安装包
# macOS: 下载 .dmg 安装包  
# Linux: 下载 .tar.gz 压缩包
```

#### 基础配置
1. **启动IDEA，选择配置**
   - 主题：Darcula（深色）或 IntelliJ Light（浅色）
   - 字体：JetBrains Mono, 14pt
   - 编码：UTF-8

2. **安装必要插件**
   ```
   File → Settings → Plugins
   
   必装插件：
   - Lombok Plugin
   - Spring Boot Helper
   - GitToolBox
   - Rainbow Brackets
   - Translation
   - Alibaba Java Coding Guidelines
   
   可选插件：
   - SonarLint
   - CheckStyle-IDEA
   - FindBugs-IDEA
   ```

3. **配置代码模板**
   ```java
   // File → Settings → Editor → File and Code Templates
   // Class模板添加：
   /**
    * ${DESCRIPTION}
    *
    * <AUTHOR>
    * @date ${DATE}
    */
   ```

### 2. Maven配置

#### 检查Maven安装
```bash
# IDEA内置Maven，检查版本
mvn -version
```

#### 配置Maven镜像（提高下载速度）
```xml
<!-- 编辑 ~/.m2/settings.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">
  
  <mirrors>
    <!-- 阿里云镜像 -->
    <mirror>
      <id>aliyunmaven</id>
      <mirrorOf>*</mirrorOf>
      <name>阿里云公共仓库</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </mirror>
  </mirrors>
  
  <profiles>
    <profile>
      <id>jdk-17</id>
      <activation>
        <activeByDefault>true</activeByDefault>
        <jdk>17</jdk>
      </activation>
      <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.compilerVersion>17</maven.compiler.compilerVersion>
      </properties>
    </profile>
  </profiles>
</settings>
```

## 🗄️ 数据库环境

### 方案A：H2数据库（推荐新手）

#### 优势
- 无需安装，内嵌在应用中
- 配置简单
- 支持Web控制台
- 适合开发和测试

#### 配置
```yaml
# application.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  h2:
    console:
      enabled: true
      path: /h2-console
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
```

### 方案B：MySQL数据库（生产环境）

#### 安装MySQL

**Windows系统**
```powershell
# 使用 Chocolatey
choco install mysql

# 或下载官方安装包
# https://dev.mysql.com/downloads/mysql/
```

**macOS系统**
```bash
# 使用 Homebrew
brew install mysql

# 启动MySQL服务
brew services start mysql

# 安全配置
mysql_secure_installation
```

**Linux系统（Ubuntu）**
```bash
# 安装MySQL
sudo apt update
sudo apt install mysql-server

# 启动服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### 数据库配置
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE youjia_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'youjia'@'localhost' IDENTIFIED BY 'youjia123';

-- 授权
GRANT ALL PRIVILEGES ON youjia_test.* TO 'youjia'@'localhost';
FLUSH PRIVILEGES;

-- 验证
SHOW DATABASES;
SELECT User, Host FROM mysql.user WHERE User = 'youjia';
```

#### Spring Boot配置
```yaml
# application-mysql.yml
spring:
  datasource:
    url: ************************************************************************************************************************
    username: youjia
    password: youjia123
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
```

## 📝 版本控制工具

### Git安装配置

#### 安装Git
```bash
# Windows (使用 Chocolatey)
choco install git

# macOS (使用 Homebrew)
brew install git

# Linux (Ubuntu)
sudo apt install git
```

#### 基础配置
```bash
# 配置用户信息
git config --global user.name "您的姓名"
git config --global user.email "您的邮箱"

# 配置默认编辑器
git config --global core.editor "code"  # 使用VS Code
# git config --global core.editor "vim"   # 使用Vim

# 配置换行符处理
git config --global core.autocrlf input   # Mac/Linux
# git config --global core.autocrlf true  # Windows

# 配置默认分支名
git config --global init.defaultBranch main

# 查看配置
git config --list
```

#### SSH密钥配置
```bash
# 生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "您的邮箱"

# 启动ssh-agent
eval "$(ssh-agent -s)"

# 添加私钥到ssh-agent
ssh-add ~/.ssh/id_rsa

# 查看公钥（复制到GitHub）
cat ~/.ssh/id_rsa.pub

# 测试连接
ssh -T **************
```

## 🔧 开发辅助工具

### 1. API测试工具 - Postman

#### 安装
```bash
# 访问官网下载
# https://www.postman.com/downloads/

# 或使用包管理器
# Windows
choco install postman

# macOS  
brew install --cask postman

# Linux
sudo snap install postman
```

#### 基础配置
1. **创建工作空间**
   - 名称：佑珈测试平台
   - 类型：Personal

2. **创建集合**
   - 用户管理API
   - 项目管理API
   - 测试用例API

3. **配置环境变量**
   ```json
   {
     "baseUrl": "http://localhost:8080",
     "token": ""
   }
   ```

### 2. 数据库客户端

#### 免费选项：DBeaver
```bash
# 下载安装
# https://dbeaver.io/download/

# 配置连接
# Host: localhost
# Port: 3306
# Database: youjia_test
# Username: youjia
# Password: youjia123
```

#### 付费选项：DataGrip
```bash
# JetBrains全家桶的一部分
# 与IDEA集成度高
# 功能强大，适合专业开发
```

### 3. 浏览器开发工具

#### Chrome扩展
- JSON Formatter：格式化JSON响应
- REST Client：直接在浏览器中测试API
- Vue.js devtools：Vue开发调试

#### Firefox扩展
- JSONView：JSON格式化显示
- RESTClient：API测试工具

## ✅ 环境验证清单

### 基础环境验证
```bash
# Java环境
java -version
# 应显示：openjdk version "17.x.x"

javac -version  
# 应显示：javac 17.x.x

echo $JAVA_HOME
# 应显示JDK安装路径

# Maven环境
mvn -version
# 应显示Maven版本和Java版本

# Git环境
git --version
# 应显示Git版本

git config --list
# 应显示配置信息
```

### 开发工具验证
- [ ] IDEA可以正常启动
- [ ] 可以创建新的Spring Boot项目
- [ ] Maven依赖可以正常下载
- [ ] 插件安装成功

### 数据库验证
```bash
# H2数据库
# 启动项目后访问：http://localhost:8080/h2-console

# MySQL数据库
mysql -u youjia -p youjia_test
# 应能正常连接
```

### 网络连接验证
```bash
# 测试Maven仓库连接
curl -I https://maven.aliyun.com/repository/public

# 测试GitHub连接
ssh -T **************

# 测试网络速度
curl -o /dev/null -s -w "%{time_total}\n" https://www.baidu.com
```

## 🚨 常见问题解决

### Java环境问题
**Q: java -version显示版本不对**
```bash
# 检查JAVA_HOME
echo $JAVA_HOME

# 检查PATH
echo $PATH | grep java

# 重新设置环境变量
export JAVA_HOME=/path/to/jdk-17
export PATH=$JAVA_HOME/bin:$PATH
```

**Q: Maven下载依赖失败**
```bash
# 检查网络连接
ping maven.aliyun.com

# 清理本地仓库
rm -rf ~/.m2/repository

# 重新下载
mvn clean install
```

### 数据库连接问题
**Q: MySQL连接被拒绝**
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 启动MySQL服务
sudo systemctl start mysql

# 检查端口占用
netstat -tlnp | grep 3306
```

**Q: H2控制台无法访问**
```yaml
# 检查配置
spring:
  h2:
    console:
      enabled: true
      path: /h2-console
```

### Git配置问题
**Q: SSH连接失败**
```bash
# 检查SSH密钥
ls -la ~/.ssh/

# 重新生成密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 添加到GitHub
cat ~/.ssh/id_rsa.pub
```

## 📋 环境搭建完成检查表

### 必需环境
- [ ] JDK 17+ 安装并配置
- [ ] IntelliJ IDEA 安装并配置
- [ ] Maven 配置完成
- [ ] Git 安装并配置
- [ ] 数据库环境准备完成

### 开发工具
- [ ] Postman 安装并配置
- [ ] 数据库客户端安装
- [ ] 浏览器开发工具准备

### 验证测试
- [ ] 所有命令行工具正常工作
- [ ] IDEA可以创建Spring Boot项目
- [ ] 数据库连接正常
- [ ] Git可以正常提交代码

---

**🎉 恭喜！您的开发环境已经搭建完成！**

现在您可以开始愉快的Java开发之旅了。如果在使用过程中遇到任何问题，请参考常见问题解决方案或寻求帮助。
