# 佑珈测试平台前期准备工作指南

## 📋 准备工作概述

在开始16周的学习计划之前，充分的准备工作是成功的关键。本指南将帮助您完成所有必要的准备工作，确保学习过程顺利进行。

## 🎯 准备工作目标

- ✅ 搭建完整的开发环境
- ✅ 掌握基础工具的使用
- ✅ 理解项目架构和技术栈
- ✅ 建立学习计划和时间管理
- ✅ 准备学习资源和参考资料

## 📅 准备工作时间表（建议1-2周）

### 第1周：环境搭建与工具准备

#### Day 1-2：开发环境搭建

**🔧 Java开发环境**
- [ ] **安装JDK 17+**
  ```bash
  # 验证安装
  java -version
  javac -version
  ```
  - 推荐：Oracle JDK 17 或 OpenJDK 17
  - 配置JAVA_HOME环境变量
  - 添加到PATH环境变量

- [ ] **安装IntelliJ IDEA**
  - 下载Community版本（免费）或Ultimate版本
  - 基础配置：主题、字体、编码格式
  - 安装必要插件：
    - Lombok Plugin
    - Spring Boot Helper
    - GitToolBox
    - Rainbow Brackets
    - Translation

**🗄️ 数据库环境**
- [ ] **选择数据库方案**
  - **方案A（推荐新手）**：使用H2内存数据库
    - 无需额外安装
    - 配置简单
    - 适合学习和开发
  
  - **方案B（生产环境）**：安装MySQL 8.0+
    - 下载并安装MySQL
    - 创建数据库：`youjia_test`
    - 创建用户并授权
    ```sql
    CREATE DATABASE youjia_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE USER 'youjia'@'localhost' IDENTIFIED BY 'password123';
    GRANT ALL PRIVILEGES ON youjia_test.* TO 'youjia'@'localhost';
    FLUSH PRIVILEGES;
    ```

**📝 版本控制工具**
- [ ] **安装Git**
  ```bash
  # 配置用户信息
  git config --global user.name "您的姓名"
  git config --global user.email "您的邮箱"
  
  # 配置默认编辑器
  git config --global core.editor "code"
  
  # 配置换行符处理
  git config --global core.autocrlf input  # Mac/Linux
  git config --global core.autocrlf true   # Windows
  ```

- [ ] **生成SSH密钥**
  ```bash
  # 生成SSH密钥
  ssh-keygen -t rsa -b 4096 -C "您的邮箱"
  
  # 查看公钥
  cat ~/.ssh/id_rsa.pub
  ```
  - 将公钥添加到GitHub账户

#### Day 3-4：开发工具配置

**🔧 API测试工具**
- [ ] **安装Postman**
  - 创建账户并登录
  - 创建工作空间：`佑珈测试平台`
  - 创建集合：`用户管理API`、`项目管理API`等

**📊 数据库管理工具**
- [ ] **选择数据库客户端**
  - **免费选项**：DBeaver、MySQL Workbench
  - **付费选项**：Navicat、DataGrip
  - 配置数据库连接

**🌐 浏览器开发工具**
- [ ] **安装Chrome/Firefox开发者扩展**
  - JSON Formatter
  - REST Client
  - Vue.js devtools（如果使用Vue前端）

#### Day 5-7：项目环境准备

**📁 项目目录结构**
- [ ] **创建工作目录**
  ```
  ~/workspace/
  ├── youjia-platform/          # 主项目目录
  ├── learning-notes/           # 学习笔记
  ├── resources/               # 学习资源
  └── backup/                  # 备份目录
  ```

**📚 学习资源准备**
- [ ] **下载离线文档**
  - Spring Boot官方文档
  - Spring Data JPA文档
  - MySQL参考手册
  - Java API文档

- [ ] **准备学习书籍**
  - 《Spring Boot实战》
  - 《Java核心技术》
  - 《MySQL必知必会》
  - 《设计模式：可复用面向对象软件的基础》

### 第2周：基础知识准备与项目理解

#### Day 1-2：Java基础回顾

**☕ Java核心概念复习**
- [ ] **面向对象编程**
  - 类和对象
  - 继承、封装、多态
  - 抽象类和接口
  - 内部类和匿名类

- [ ] **Java集合框架**
  - List、Set、Map的使用
  - ArrayList vs LinkedList
  - HashMap vs TreeMap
  - 迭代器的使用

- [ ] **异常处理**
  - try-catch-finally
  - 自定义异常
  - 异常链
  - 最佳实践

**🧪 实践练习**
```java
// 创建简单的练习项目
public class JavaBasicsReview {
    // 练习1：设计一个用户类
    public static class User {
        private String username;
        private String email;
        // 构造器、getter、setter、toString
    }
    
    // 练习2：集合操作
    public static void collectionPractice() {
        List<User> users = new ArrayList<>();
        // 添加、删除、查找操作
    }
    
    // 练习3：异常处理
    public static void exceptionPractice() {
        try {
            // 可能抛出异常的代码
        } catch (Exception e) {
            // 异常处理
        }
    }
}
```

#### Day 3-4：Spring基础概念学习

**🌱 Spring核心概念**
- [ ] **IoC（控制反转）**
  - 理解依赖注入的概念
  - 构造器注入 vs Setter注入
  - @Autowired注解的使用

- [ ] **AOP（面向切面编程）**
  - 切面、切点、通知的概念
  - 常见应用场景：日志、事务、安全

- [ ] **Spring注解**
  - @Component、@Service、@Repository、@Controller
  - @Configuration、@Bean
  - @Value、@Profile

**📖 学习资源**
- Spring官方文档入门指南
- 在线教程：Spring Boot快速入门
- 视频教程：Spring基础概念讲解

#### Day 5-7：项目架构理解

**🏗️ 项目结构分析**
- [ ] **克隆项目代码**
  ```bash
  git clone https://github.com/YoGazz/yoga--system.git
  cd yoga--system
  ```

- [ ] **分析项目结构**
  ```
  src/main/java/com/yoga/youjia/
  ├── YoujiaApplication.java     # 启动类
  ├── controller/               # 控制器层
  ├── service/                 # 业务逻辑层
  ├── repository/              # 数据访问层
  ├── entity/                  # 实体类
  ├── dto/                     # 数据传输对象
  ├── config/                  # 配置类
  └── common/                  # 通用类
  ```

- [ ] **理解技术栈**
  - Spring Boot 3.x
  - Spring Data JPA
  - Spring Security
  - H2/MySQL数据库
  - Maven构建工具

**🔍 代码分析练习**
- [ ] **分析现有代码**
  - 查看User实体类的设计
  - 理解UserController的接口设计
  - 分析UserService的业务逻辑
  - 了解数据库表结构

- [ ] **运行项目**
  ```bash
  # 使用Maven运行
  mvn spring-boot:run
  
  # 或在IDEA中运行YoujiaApplication.java
  ```

- [ ] **测试现有功能**
  - 访问H2控制台：http://localhost:8080/h2-console
  - 使用Postman测试用户注册/登录接口
  - 查看数据库中的数据变化

## 🛠️ 开发环境验证清单

### 基础环境验证
- [ ] Java版本正确：`java -version` 显示17+
- [ ] Maven可用：`mvn -version` 正常显示
- [ ] Git配置正确：`git config --list` 显示用户信息
- [ ] IDEA正常启动并可以创建Spring Boot项目

### 项目环境验证
- [ ] 项目可以正常导入IDEA
- [ ] Maven依赖下载成功
- [ ] 项目可以正常启动
- [ ] H2数据库控制台可以访问
- [ ] 现有API接口测试通过

### 工具验证
- [ ] Postman可以发送HTTP请求
- [ ] 数据库客户端可以连接数据库
- [ ] Git可以正常提交和推送代码

## 📚 学习资源整理

### 在线文档
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [Spring Data JPA文档](https://spring.io/projects/spring-data-jpa)
- [MySQL官方文档](https://dev.mysql.com/doc/)
- [Maven官方文档](https://maven.apache.org/guides/)

### 视频教程
- B站：Spring Boot从入门到精通
- 慕课网：Spring Boot实战课程
- 极客时间：Java核心技术36讲

### 书籍推荐
- 《Spring Boot实战》- Craig Walls
- 《Java核心技术 卷I》- Cay S. Horstmann
- 《Effective Java》- Joshua Bloch
- 《设计模式》- GoF

### 实践网站
- [LeetCode](https://leetcode.com/) - 算法练习
- [GitHub](https://github.com/) - 开源项目学习
- [Stack Overflow](https://stackoverflow.com/) - 问题解答
- [Baeldung](https://www.baeldung.com/) - Java教程

## 🎯 学习计划制定

### 时间安排建议
- **工作日**：每天2-3小时（晚上或早上）
- **周末**：每天4-5小时
- **总计**：每周15-20小时

### 学习方法建议
1. **理论学习**（30%）：阅读文档、观看视频
2. **实践编码**（60%）：动手写代码、完成任务
3. **总结反思**（10%）：记录笔记、整理问题

### 进度跟踪
- [ ] 建立学习日志
- [ ] 每周进行自我评估
- [ ] 记录遇到的问题和解决方案
- [ ] 定期回顾和调整学习计划

## 🚨 常见问题与解决方案

### 环境搭建问题
**Q: JDK安装后java -version显示版本不对？**
A: 检查JAVA_HOME环境变量和PATH配置，确保指向正确的JDK目录。

**Q: IDEA导入项目后Maven依赖下载失败？**
A: 检查网络连接，配置Maven镜像源（如阿里云镜像）。

**Q: 项目启动时端口被占用？**
A: 修改application.yml中的server.port配置，或关闭占用端口的程序。

### 学习方法问题
**Q: 感觉学习内容太多，不知道从哪里开始？**
A: 严格按照学习路线图的顺序，一步一步来，不要跳跃。

**Q: 遇到不懂的概念怎么办？**
A: 先查阅官方文档，再搜索相关教程，最后寻求帮助。

**Q: 代码写不出来怎么办？**
A: 先理解需求，再参考示例代码，逐步实现功能。

## ✅ 准备工作完成检查

### 环境准备完成度
- [ ] 开发环境搭建完成（100%）
- [ ] 工具配置完成（100%）
- [ ] 项目环境验证通过（100%）
- [ ] 学习资源准备完成（100%）

### 基础知识准备度
- [ ] Java基础概念复习完成（80%+）
- [ ] Spring基础概念理解（60%+）
- [ ] 项目架构理解（70%+）
- [ ] 开发工具熟练使用（80%+）

### 学习计划准备度
- [ ] 时间安排制定完成
- [ ] 学习方法确定
- [ ] 进度跟踪机制建立
- [ ] 问题解决渠道准备

---

**🎉 恭喜！完成所有准备工作后，您就可以开始正式的16周学习计划了！**

记住：充分的准备是成功的一半。花时间做好准备工作，会让后续的学习过程更加顺利和高效。

准备好了吗？让我们开始这段精彩的学习之旅吧！🚀
