# 佑珈测试平台开发规范

## 📋 概述

本文档定义了佑珈测试平台的开发规范，旨在确保代码质量、提高开发效率、便于团队协作。

## 🏗️ 项目结构规范

### 包结构规范
```
com.yoga.youjia
├── YoujiaApplication.java              # 主启动类
├── config/                             # 配置类
│   ├── SecurityConfig.java             # 安全配置
│   ├── WebConfig.java                  # Web配置
│   ├── DatabaseConfig.java             # 数据库配置
│   └── CacheConfig.java                # 缓存配置
├── controller/                         # 控制器层
│   ├── AuthController.java             # 认证控制器
│   ├── UserController.java             # 用户控制器
│   ├── ProjectController.java          # 项目控制器
│   └── ...
├── service/                            # 业务逻辑层
│   ├── AuthService.java                # 认证服务
│   ├── UserService.java                # 用户服务
│   ├── impl/                           # 服务实现类
│   │   ├── AuthServiceImpl.java
│   │   └── UserServiceImpl.java
│   └── ...
├── repository/                         # 数据访问层
│   ├── UserRepository.java             # 用户数据访问
│   ├── ProjectRepository.java          # 项目数据访问
│   └── ...
├── entity/                             # 实体类
│   ├── User.java                       # 用户实体
│   ├── Project.java                    # 项目实体
│   └── ...
├── dto/                                # 数据传输对象
│   ├── request/                        # 请求DTO
│   │   ├── LoginRequest.java
│   │   ├── RegisterRequest.java
│   │   └── ...
│   ├── response/                       # 响应DTO
│   │   ├── LoginResponse.java
│   │   ├── UserResponse.java
│   │   └── ...
│   └── ...
├── enums/                              # 枚举类
│   ├── UserRole.java                   # 用户角色
│   ├── UserStatus.java                 # 用户状态
│   └── ...
├── exception/                          # 异常类
│   ├── BusinessException.java          # 业务异常
│   ├── ResourceNotFoundException.java  # 资源未找到异常
│   └── ...
├── security/                           # 安全相关
│   ├── filter/                         # 过滤器
│   ├── service/                        # 安全服务
│   └── util/                           # 安全工具类
├── util/                               # 工具类
│   ├── JwtUtil.java                    # JWT工具类
│   ├── DateUtil.java                   # 日期工具类
│   └── ...
├── common/                             # 通用类
│   ├── ApiResponse.java                # 统一响应格式
│   ├── ResultCode.java                 # 结果码
│   ├── Constants.java                  # 常量类
│   └── ...
└── aspect/                             # 切面类
    ├── LoggingAspect.java              # 日志切面
    └── ...
```

## 📝 命名规范

### 类命名规范
- **Controller类**: 以`Controller`结尾，如`UserController`
- **Service接口**: 以`Service`结尾，如`UserService`
- **Service实现**: 以`ServiceImpl`结尾，如`UserServiceImpl`
- **Repository类**: 以`Repository`结尾，如`UserRepository`
- **Entity类**: 使用名词，如`User`、`Project`
- **DTO类**: 以用途结尾，如`LoginRequest`、`UserResponse`
- **Exception类**: 以`Exception`结尾，如`BusinessException`
- **Util类**: 以`Util`结尾，如`JwtUtil`

### 方法命名规范
- **Controller方法**: 使用HTTP动词风格
  ```java
  @GetMapping("/users/{id}")
  public ApiResponse<UserResponse> getUser(@PathVariable Long id)
  
  @PostMapping("/users")
  public ApiResponse<UserResponse> createUser(@RequestBody CreateUserRequest request)
  
  @PutMapping("/users/{id}")
  public ApiResponse<UserResponse> updateUser(@PathVariable Long id, @RequestBody UpdateUserRequest request)
  
  @DeleteMapping("/users/{id}")
  public ApiResponse<Void> deleteUser(@PathVariable Long id)
  ```

- **Service方法**: 使用业务语义
  ```java
  // 查询方法
  User findById(Long id);
  List<User> findByStatus(UserStatus status);
  Page<User> findByCondition(UserQueryCondition condition, Pageable pageable);
  
  // 业务方法
  User createUser(CreateUserRequest request);
  User updateUser(Long id, UpdateUserRequest request);
  void deleteUser(Long id);
  void enableUser(Long id);
  void disableUser(Long id);
  ```

### 变量命名规范
- 使用驼峰命名法
- 布尔变量使用`is`、`has`、`can`等前缀
- 集合变量使用复数形式
- 常量使用大写字母和下划线

```java
// 正确示例
private String userName;
private boolean isActive;
private List<User> userList;
private static final String DEFAULT_PASSWORD = "123456";

// 错误示例
private String user_name;
private boolean active;
private List<User> user;
private static final String defaultPassword = "123456";
```

## 🎯 编码规范

### 注解使用规范

#### 实体类注解
```java
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "username", nullable = false, unique = true, length = 50)
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;
    
    @Column(name = "email", nullable = false, unique = true, length = 100)
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UserStatus status;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
```

#### Controller类注解
```java
@RestController
@RequestMapping("/api/users")
@Validated
@Slf4j
@Tag(name = "用户管理", description = "用户相关接口")
public class UserController {
    
    @GetMapping("/{id}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    public ApiResponse<UserResponse> getUser(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @Min(1) Long id) {
        // 实现逻辑
    }
}
```

#### Service类注解
```java
@Service
@Transactional(readOnly = true)
@Slf4j
public class UserServiceImpl implements UserService {
    
    @Transactional
    @Override
    public User createUser(CreateUserRequest request) {
        // 实现逻辑
    }
}
```

### 异常处理规范

#### 自定义异常类
```java
@Getter
public class BusinessException extends RuntimeException {
    private final String code;
    private final String message;
    
    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }
}
```

#### 全局异常处理
```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public ApiResponse<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return ApiResponse.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.warn("参数验证失败: {}", message);
        return ApiResponse.error("VALIDATION_ERROR", message);
    }
}
```

### 日志规范

#### 日志级别使用
- **ERROR**: 系统错误，需要立即处理
- **WARN**: 警告信息，需要关注但不影响系统运行
- **INFO**: 重要的业务流程信息
- **DEBUG**: 调试信息，生产环境不输出

#### 日志格式规范
```java
@Service
@Slf4j
public class UserServiceImpl implements UserService {
    
    @Override
    public User createUser(CreateUserRequest request) {
        log.info("开始创建用户, username: {}", request.getUsername());
        
        try {
            // 业务逻辑
            User user = userRepository.save(newUser);
            log.info("用户创建成功, userId: {}, username: {}", user.getId(), user.getUsername());
            return user;
        } catch (Exception e) {
            log.error("用户创建失败, username: {}, error: {}", request.getUsername(), e.getMessage(), e);
            throw new BusinessException("USER_CREATE_FAILED", "用户创建失败");
        }
    }
}
```

## 🗄️ 数据库规范

### 表命名规范
- 使用小写字母和下划线
- 表名使用复数形式
- 关联表使用两个表名组合

```sql
-- 正确示例
users
projects
test_cases
user_projects

-- 错误示例
User
project
testCase
userProject
```

### 字段命名规范
- 使用小写字母和下划线
- 主键统一使用`id`
- 外键使用`表名_id`格式
- 时间字段使用`_at`后缀

```sql
-- 正确示例
id
user_id
project_id
created_at
updated_at

-- 错误示例
userId
projectId
createTime
updateTime
```

### 索引规范
- 主键索引: `pk_表名`
- 唯一索引: `uk_表名_字段名`
- 普通索引: `idx_表名_字段名`
- 复合索引: `idx_表名_字段1_字段2`

## 📡 API设计规范

### RESTful API规范
```http
# 资源操作
GET    /api/users           # 获取用户列表
GET    /api/users/{id}      # 获取指定用户
POST   /api/users           # 创建用户
PUT    /api/users/{id}      # 更新用户
DELETE /api/users/{id}      # 删除用户

# 子资源操作
GET    /api/users/{id}/projects     # 获取用户的项目列表
POST   /api/users/{id}/projects     # 为用户添加项目

# 动作操作
PUT    /api/users/{id}/enable       # 启用用户
PUT    /api/users/{id}/disable      # 禁用用户
POST   /api/users/{id}/reset-password  # 重置密码
```

### 响应格式规范
```java
// 统一响应格式
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    private boolean success;
    private String code;
    private String message;
    private T data;
    private Long timestamp;
    
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .success(true)
                .code("SUCCESS")
                .message("操作成功")
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    public static <T> ApiResponse<T> error(String code, String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .code(code)
                .message(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
```

### 分页响应格式
```java
@Data
@Builder
public class PageResponse<T> {
    private List<T> content;
    private int page;
    private int size;
    private long totalElements;
    private int totalPages;
    private boolean first;
    private boolean last;
}
```

## 🧪 测试规范

### 单元测试规范
```java
@ExtendWith(MockitoExtension.class)
class UserServiceImplTest {
    
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private UserServiceImpl userService;
    
    @Test
    @DisplayName("创建用户 - 成功")
    void createUser_Success() {
        // Given
        CreateUserRequest request = CreateUserRequest.builder()
                .username("testuser")
                .email("<EMAIL>")
                .build();
        
        User savedUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .build();
        
        when(userRepository.save(any(User.class))).thenReturn(savedUser);
        
        // When
        User result = userService.createUser(request);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getUsername()).isEqualTo("testuser");
        verify(userRepository).save(any(User.class));
    }
    
    @Test
    @DisplayName("创建用户 - 用户名已存在")
    void createUser_UsernameExists() {
        // Given
        CreateUserRequest request = CreateUserRequest.builder()
                .username("existuser")
                .build();
        
        when(userRepository.existsByUsername("existuser")).thenReturn(true);
        
        // When & Then
        assertThatThrownBy(() -> userService.createUser(request))
                .isInstanceOf(BusinessException.class)
                .hasMessage("用户名已存在");
    }
}
```

### 集成测试规范
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
class UserControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    @DisplayName("创建用户接口测试")
    void createUser_Integration() {
        // Given
        CreateUserRequest request = CreateUserRequest.builder()
                .username("integrationtest")
                .email("<EMAIL>")
                .password("123456")
                .build();
        
        // When
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
                "/api/users", request, ApiResponse.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
        
        // 验证数据库
        Optional<User> savedUser = userRepository.findByUsername("integrationtest");
        assertThat(savedUser).isPresent();
    }
}
```

## 📋 代码审查清单

### 功能性检查
- [ ] 功能实现是否符合需求
- [ ] 边界条件是否处理
- [ ] 异常情况是否处理
- [ ] 输入验证是否完整

### 代码质量检查
- [ ] 命名是否规范
- [ ] 代码结构是否清晰
- [ ] 注释是否充分
- [ ] 是否有重复代码

### 性能检查
- [ ] 数据库查询是否优化
- [ ] 是否存在N+1查询问题
- [ ] 缓存使用是否合理
- [ ] 资源是否正确释放

### 安全检查
- [ ] 输入是否验证和过滤
- [ ] 权限控制是否正确
- [ ] 敏感信息是否加密
- [ ] SQL注入是否防护

### 测试检查
- [ ] 单元测试是否充分
- [ ] 测试用例是否覆盖主要场景
- [ ] 测试数据是否合理
- [ ] 测试是否可重复执行

---
