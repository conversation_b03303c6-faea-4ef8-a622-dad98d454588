# 佑珈测试平台核心功能设计图

## 📋 设计图概览

本文档包含了佑珈测试平台的核心功能设计图，从系统架构到具体业务流程，为开发团队提供清晰的技术指导和业务理解。

## 🏗️ 1. 系统架构图

### 架构层次说明

#### **前端层 (Frontend Layer)**
- **Vue.js 用户界面**: 基于Vue.js 3.x的响应式用户界面
- **Vue Router 路由**: 单页应用路由管理
- **Pinia 状态管理**: 全局状态管理
- **Axios HTTP客户端**: API请求处理

#### **网关层 (Gateway Layer)**
- **API网关**: 统一入口，路由分发
- **认证中心**: JWT令牌验证和用户认证
- **限流控制**: API访问频率限制

#### **应用层 (Application Layer)**
- **用户管理**: 用户CRUD、角色权限管理
- **项目管理**: 项目生命周期管理、成员管理
- **需求管理**: 需求创建、审批、状态流转
- **测试用例**: 用例编写、模块管理、批量操作
- **测试执行**: 计划创建、用例执行、结果统计
- **缺陷管理**: 缺陷跟踪、状态流转、通知

#### **数据层 (Data Layer)**
- **关系数据库**: 存储业务数据
- **Redis缓存**: 提高查询性能
- **文件存储**: 附件和文档管理

#### **基础设施 (Infrastructure)**
- **配置中心**: 统一配置管理
- **日志系统**: 操作日志和错误日志
- **监控系统**: 系统性能监控
- **安全组件**: 安全策略和防护

### 技术选型理由

1. **Spring Boot**: 快速开发、自动配置、生态完善
2. **Vue.js**: 渐进式框架、学习成本低、社区活跃
3. **MySQL**: 成熟稳定、性能优秀、运维简单
4. **Redis**: 高性能缓存、丰富数据结构
5. **JWT**: 无状态认证、跨域支持

## 🔄 2. 核心业务流程图

### 主要业务流程

#### **项目管理流程**
1. **创建项目** → **添加成员** → **设置权限** → **项目就绪**
2. 支持项目生命周期管理
3. 灵活的成员角色分配

#### **需求管理流程**
1. **创建需求** → **需求评审** → **评审通过/修订** → **需求激活**
2. 状态驱动的工作流
3. 支持需求变更管理

#### **测试用例管理流程**
1. **创建模块** → **编写用例** → **用例评审** → **用例就绪**
2. 树形模块结构
3. 标准化用例模板

#### **测试执行流程**
1. **创建计划** → **选择用例** → **分配人员** → **执行测试** → **记录结果**
2. 支持并行执行
3. 实时进度跟踪

#### **缺陷管理流程**
1. **报告缺陷** → **分配开发** → **修复缺陷** → **验证修复** → **关闭缺陷**
2. 完整的缺陷生命周期
3. 自动化通知机制

### 流程集成特点

- **数据关联**: 需求→用例→执行→缺陷的完整追溯链
- **状态同步**: 各模块状态实时同步更新
- **权限控制**: 基于角色的操作权限控制
- **通知机制**: 关键节点自动通知相关人员

## 🗄️ 3. 数据库实体关系图 (ERD)

### 核心实体设计

#### **用户相关实体**
- **USERS**: 用户基础信息、角色、状态
- **PROJECT_MEMBERS**: 项目成员关系、项目内角色

#### **项目相关实体**
- **PROJECTS**: 项目基础信息、状态、时间范围
- **REQUIREMENTS**: 需求信息、类型、优先级、状态

#### **测试相关实体**
- **TEST_MODULES**: 测试模块树形结构
- **TEST_CASES**: 测试用例详细信息
- **TEST_CASE_STEPS**: 测试步骤详细描述
- **TEST_PLANS**: 测试计划信息
- **TEST_PLAN_CASES**: 计划与用例关联、执行结果

#### **缺陷相关实体**
- **BUGS**: 缺陷详细信息、状态流转
- **BUG_ATTACHMENTS**: 缺陷附件管理

#### **系统相关实体**
- **NOTIFICATIONS**: 系统通知
- **AUDIT_LOGS**: 操作审计日志

### 关系设计原则

1. **数据完整性**: 外键约束保证数据一致性
2. **查询优化**: 合理的索引设计
3. **扩展性**: 预留扩展字段
4. **审计追踪**: 完整的操作记录

## 👥 4. 用户权限与角色设计

### 角色层次结构

#### **系统管理员 (ADMIN)**
- 最高权限，管理整个系统
- 用户管理、系统配置、数据导出

#### **项目经理 (PROJECT_MANAGER)**
- 项目级别的管理权限
- 项目管理、需求管理、测试管理、缺陷管理

#### **测试人员 (TESTER)**
- 测试相关的操作权限
- 测试用例管理、测试执行、缺陷报告

#### **开发人员 (DEVELOPER)**
- 开发相关的权限
- 缺陷处理、查看项目信息

#### **观察者 (VIEWER)**
- 只读权限
- 查看项目、需求、用例、缺陷信息

### 权限控制策略

1. **基于角色的访问控制 (RBAC)**
2. **项目级别的权限隔离**
3. **操作级别的细粒度控制**
4. **动态权限验证**

## 🔄 5. 测试执行状态流转图

### 状态流转设计

#### **测试计划状态**
- 计划创建 → 用例选择 → 人员分配 → 计划就绪 → 执行中 → 计划完成

#### **用例执行状态**
- 未执行 → 执行中 → 通过/失败/阻塞/跳过

#### **缺陷处理状态**
- 新建 → 已分配 → 已解决 → 已验证 → 已关闭
- 支持重新打开和重新分配

### 状态流转规则

1. **状态约束**: 只能按规定路径流转
2. **权限控制**: 不同角色可操作的状态转换
3. **自动触发**: 某些状态自动触发后续操作
4. **历史记录**: 完整的状态变更历史

## 🔌 6. API接口设计

### RESTful API设计原则

#### **统一的URL结构**
- 资源导向的URL设计
- 标准的HTTP方法使用
- 一致的响应格式

#### **API分组设计**
- **认证授权**: `/api/auth/*`
- **用户管理**: `/api/users/*`
- **项目管理**: `/api/projects/*`
- **需求管理**: `/api/requirements/*`
- **测试用例**: `/api/testcases/*`
- **测试执行**: `/api/execution/*`
- **缺陷管理**: `/api/bugs/*`
- **系统管理**: `/api/system/*`

#### **接口特性**
- **版本控制**: 支持API版本管理
- **分页查询**: 统一的分页参数
- **条件筛选**: 灵活的查询条件
- **批量操作**: 支持批量数据处理

### API安全设计

1. **JWT认证**: 无状态的令牌认证
2. **权限验证**: 接口级别的权限控制
3. **参数验证**: 严格的输入验证
4. **限流控制**: 防止API滥用

## 📊 设计图使用指南

### 开发阶段使用

#### **需求分析阶段**
- 参考业务流程图理解业务逻辑
- 使用ERD图设计数据模型

#### **系统设计阶段**
- 基于架构图进行技术选型
- 参考权限设计图设计安全策略

#### **开发实施阶段**
- 按照API设计图实现接口
- 参考状态流转图实现业务逻辑

#### **测试验证阶段**
- 基于流程图设计测试用例
- 验证状态流转的正确性

### 维护阶段使用

1. **功能扩展**: 参考现有设计保持一致性
2. **性能优化**: 基于架构图识别瓶颈点
3. **问题排查**: 利用流程图定位问题环节
4. **文档更新**: 及时更新设计图保持同步

## 🎯 设计原则总结

### 技术原则

1. **分层架构**: 清晰的层次划分，职责分离
2. **松耦合**: 模块间低耦合，高内聚
3. **可扩展**: 支持功能和性能的横向扩展
4. **可维护**: 代码结构清晰，易于维护

### 业务原则

1. **用户导向**: 以用户体验为中心的设计
2. **流程标准**: 标准化的业务流程
3. **数据一致**: 保证数据的完整性和一致性
4. **安全可靠**: 完善的安全机制和容错处理

### 开发原则

1. **渐进式**: 支持分阶段开发和部署
2. **标准化**: 遵循行业标准和最佳实践
3. **文档化**: 完善的设计文档和接口文档
4. **测试驱动**: 重视测试，保证代码质量

---

这些设计图为佑珈测试平台的开发提供了完整的技术指导，确保系统的架构合理、功能完善、易于维护。在实际开发过程中，请严格按照这些设计进行实施，并根据实际情况进行适当调整。
