# ================================
# ä½çæµè¯ç®¡çå¹³å° - å¼åç¯å¢éç½®
# ================================
# æ­¤éç½®æä»¶ç¨äºå¼åç¯å¢ï¼ä½¿ç¨H2åå­æ°æ®åº
# çäº§ç¯å¢è¯·ä½¿ç¨application-prod.properties

# ================================
# æå¡å¨éç½®
# ================================
server.port=8081
server.servlet.context-path=/
# å¯ç¨ååºåç¼©
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
# è¿æ¥è¶æ¶éç½®
server.tomcat.connection-timeout=60000
server.tomcat.keep-alive-timeout=60000

# ================================
# æ°æ®åºéç½® - H2åå­æ°æ®åºï¼å¼åç¯å¢ï¼
# ================================
spring.datasource.url=jdbc:h2:mem:youjia;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
# è¿æ¥æ± éç½®
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000

# H2æ§å¶å°éç½®
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=true

# ================================
# JPA/Hibernateéç½®
# ================================
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
# æ ¼å¼åSQLè¾åºï¼ä¾¿äºè°è¯
spring.jpa.properties.hibernate.format_sql=true
# å¯ç¨SQLæ³¨é
spring.jpa.properties.hibernate.use_sql_comments=true
# ç¦ç¨å¼æ¾å¨è§å¾ä¸­ï¼é¿åæå è½½é®é¢ï¼
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=false
# æ¹éå¤çéç½®
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# ================================
# Jackson JSONéç½®
# ================================
spring.jackson.time-zone=Asia/Shanghai
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
# åºååæ¶å¿½ç¥nullå¼
spring.jackson.default-property-inclusion=NON_NULL
# ååºååæ¶å¿½ç¥æªç¥å±æ§
spring.jackson.deserialization.fail-on-unknown-properties=false
# ä½¿ç¨æä¸¾çtoStringæ¹æ³
spring.jackson.serialization.write-enums-using-to-string=true

# ================================
# æ¥å¿éç½®
# ================================
# æ ¹æ¥å¿çº§å«
logging.level.root=INFO
# åºç¨ç¨åºæ¥å¿çº§å«
logging.level.com.yoga.youjia=DEBUG
# SQLæ¥å¿éç½®
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
# Spring Securityæ¥å¿
logging.level.org.springframework.security=DEBUG
# è¯·æ±æ¥å¿
logging.level.org.springframework.web=DEBUG
# æ¥å¿è¾åºæ ¼å¼
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx

# ================================
# JWTä»¤çéç½®
# ================================
# JWTå¯é¥ï¼å¼åç¯å¢ä½¿ç¨ï¼çäº§ç¯å¢å¿é¡»æ´æ¢ï¼
jwt.secret=YourSecureJWTSecretKeyForDevelopmentEnvironmentMustBeAtLeast256BitsLongForHS256Algorithm123456789
# JWTä»¤çè¿ææ¶é´ï¼æ¯«ç§ï¼ - 24å°æ¶
jwt.expirationTime=86400000
# JWTè¯·æ±å¤´åç§°
jwt.header=Authorization
# JWTä»¤çåç¼
jwt.tokenPrefix=Bearer 
# JWTåè¡è
jwt.issuer=youjia-test-platform
# JWTä¸»é¢
jwt.subject=youjia-user-auth

# ================================
# å®å¨éç½®
# ================================
# CORSéç½®
security.cors.allowed-origins=*
security.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
security.cors.allowed-headers=*
security.cors.allow-credentials=true
security.cors.max-age=3600

# å¯ç å å¯å¼ºåº¦éç½®
security.password.encoder.strength=10

# è´¦æ·éå®éç½®
security.account.max-login-attempts=5
security.account.lock-duration=30

# ================================
# APIææ¡£éç½® - Knife4j
# ================================
knife4j.enable=true
knife4j.production=false
knife4j.basic.enable=false
# å¼åç¯å¢åè®¸è·¨åè®¿é®
knife4j.cors=true

# ================================
# OpenAPIææ¡£éç½®
# ================================
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/v3/api-docs
# æå®è¦æ«æçControlleråè·¯å¾
springdoc.packages-to-scan=com.yoga.youjia.controller
# APIææ¡£ä¿¡æ¯
springdoc.info.title=ä½çæµè¯ç®¡çå¹³å° API
springdoc.info.description=æä¾å®æ´çæµè¯ç®¡çåè½ï¼åæ¬ç¨æ·ç®¡çãé¡¹ç®ç®¡çãæµè¯ç¨ä¾ç®¡çç­
springdoc.info.version=1.0.0
springdoc.info.contact.name=ä½çå¢é
springdoc.info.contact.email=<EMAIL>

# ================================
# å¥åº·æ£æµåçæ§éç½® - Actuator
# ================================
# å¯ç¨ææç®¡çç«¯ç¹ï¼å¼åç¯å¢ï¼
management.endpoints.web.exposure.include=*
# å¥åº·æ£æµç«¯ç¹éç½®
management.endpoint.health.show-details=always
management.health.defaults.enabled=true
# åºç¨ä¿¡æ¯éç½®
management.info.env.enabled=true
management.info.java.enabled=true
management.info.os.enabled=true
# ææ ç«¯ç¹éç½®
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
# ç®¡çç«¯ç¹åºç¡è·¯å¾
management.endpoints.web.base-path=/actuator
# ç¦ç¨ææä¿¡æ¯å±ç¤ºï¼çäº§ç¯å¢åºè¯¥ç¦ç¨ï¼
management.endpoint.env.show-values=ALWAYS

# ================================
# åºç¨ä¿¡æ¯éç½®
# ================================
info.app.name=ä½çæµè¯ç®¡çå¹³å°
info.app.description=åºäºSpring Bootçä¼ä¸çº§æµè¯ç®¡çå¹³å°
info.app.version=@project.version@
info.app.encoding=@project.build.sourceEncoding@
info.app.java.version=@java.version@
info.app.profiles.active=@spring.profiles.active@
info.app.build.time=@maven.build.timestamp@

# ================================
# æä»¶ä¸ä¼ éç½®
# ================================
# åä¸ªæä»¶æå¤§å°ºå¯¸
spring.servlet.multipart.max-file-size=10MB
# è¯·æ±æå¤§å°ºå¯¸
spring.servlet.multipart.max-request-size=50MB
# æä»¶åå¥ç£ççéå¼
spring.servlet.multipart.file-size-threshold=2KB

# ================================
# å½éåéç½®
# ================================
spring.messages.basename=messages
spring.messages.encoding=UTF-8
spring.messages.cache-duration=3600

# ================================
# å¼åå·¥å·éç½®
# ================================
# å¯ç¨å¼åå·¥å·ï¼èªå¨éå¯ç­ï¼
spring.devtools.restart.enabled=true
# æé¤ä¸éè¦éå¯çèµæº
spring.devtools.restart.exclude=static/**,public/**,templates/**
# LiveReloadéç½®
spring.devtools.livereload.enabled=true

# ================================
# ç¼å­éç½®ï¼å¼åç¯å¢ç¦ç¨ï¼
# ================================
spring.cache.type=none

# ================================
# æ°æ®éªè¯éç½®
# ================================
# å¯ç¨å¤±è´¥å¿«éè¿å
spring.validation.fail-fast=true

# ================================
# æ§è½çæ§éç½®
# ================================
# å¯ç¨æ§è½çæ§
management.metrics.enable.all=true
# JVMææ 
management.metrics.enable.jvm=true
# ç³»ç»ææ 
management.metrics.enable.system=true
# Webææ 
management.metrics.enable.web=true

# ================================
# å¼æ­¥ä»»å¡éç½®
# ================================
spring.task.execution.pool.core-size=2
spring.task.execution.pool.max-size=10
spring.task.execution.pool.queue-capacity=1000
spring.task.execution.thread-name-prefix=youjia-task-
spring.task.execution.shutdown.await-termination=true
spring.task.execution.shutdown.await-termination-period=60s

# ================================
# å¼åç¯å¢ç¹æ®éç½®
# ================================
# å¯ç¨è°è¯æ¨¡å¼
debug=false
# å¯ç¨è¯¦ç»è¿½è¸ª
trace=false
# æ¾ç¤ºå¯å¨banner
spring.main.banner-mode=console
